﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\menu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\radar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\dx11\moverlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\alone\static\lockdown-protocol-external-master\overlay\imgui\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\alone\static\lockdown-protocol-external-master\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{5C777EB1-940F-38A8-BEA9-3CB6D356F8E0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
