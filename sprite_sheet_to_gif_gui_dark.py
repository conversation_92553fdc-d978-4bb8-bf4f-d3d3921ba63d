import os
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
from concurrent.futures import ThreadPoolExecutor
import time
import json
from tkinter import ttk
from PIL import Image, ImageTk
import logging


class DarkWalletStyle:
    """暗色钱包主题样式定义 - 仿WPF Dark Wallet Payment"""
    
    # 暗色主题颜色方案
    # 主背景色 - 深灰黑色
    BG_COLOR = "#1e1e1e"
    BG_SECONDARY = "#2d2d30"
    BG_TERTIARY = "#3e3e42"
    
    # 卡片和面板背景
    CARD_BG = "#252526"
    PANEL_BG = "#2d2d30"
    
    # 主色调 - 现代蓝色
    PRIMARY = "#007acc"
    PRIMARY_HOVER = "#1f9bcf"
    PRIMARY_ACTIVE = "#005a9e"
    PRIMARY_LIGHT = "#4fc3f7"
    
    # 辅助色调
    SECONDARY = "#6c757d"
    SUCCESS = "#28a745"
    WARNING = "#ffc107"
    DANGER = "#dc3545"
    INFO = "#17a2b8"
    
    # 文本颜色
    TEXT_PRIMARY = "#ffffff"
    TEXT_SECONDARY = "#cccccc"
    TEXT_MUTED = "#999999"
    TEXT_DISABLED = "#666666"
    
    # 边框和分割线
    BORDER_COLOR = "#3e3e42"
    BORDER_LIGHT = "#4a4a4a"
    DIVIDER = "#404040"
    
    # 输入框颜色
    INPUT_BG = "#3c3c3c"
    INPUT_BORDER = "#555555"
    INPUT_FOCUS = "#007acc"
    
    # 按钮颜色
    BUTTON_BG = "#0e639c"
    BUTTON_HOVER = "#1177bb"
    BUTTON_ACTIVE = "#0d5a8a"
    BUTTON_SECONDARY = "#6c757d"
    BUTTON_SECONDARY_HOVER = "#7a8288"
    
    # 字体
    FONT_FAMILY = "Segoe UI"
    FONT = (FONT_FAMILY, 9)
    FONT_BOLD = (FONT_FAMILY, 9, "bold")
    FONT_LARGE = (FONT_FAMILY, 12, "bold")
    FONT_SMALL = (FONT_FAMILY, 8)
    
    # 组件尺寸
    ENTRY_HEIGHT = 32
    BUTTON_HEIGHT = 36
    PADDING = 12
    CARD_PADDING = 16
    BORDER_RADIUS = 6

    @staticmethod
    def apply_theme(root):
        """应用暗色钱包主题到root窗口"""
        style = ttk.Style()
        
        # 设置全局样式
        style.configure(".", 
                      font=DarkWalletStyle.FONT,
                      background=DarkWalletStyle.BG_COLOR,
                      foreground=DarkWalletStyle.TEXT_PRIMARY)
        
        # 设置按钮样式
        style.configure("TButton",
                       background=DarkWalletStyle.BUTTON_BG,
                       foreground=DarkWalletStyle.TEXT_PRIMARY,
                       borderwidth=0,
                       focusthickness=0,
                       focuscolor=DarkWalletStyle.PRIMARY,
                       relief="flat",
                       padding=(10, 6))
        
        style.map("TButton",
                 background=[('active', DarkWalletStyle.BUTTON_HOVER),
                           ('pressed', DarkWalletStyle.BUTTON_ACTIVE)])
        
        # 设置输入框样式
        style.configure("TEntry",
                       fieldbackground=DarkWalletStyle.INPUT_BG,
                       borderwidth=1,
                       bordercolor=DarkWalletStyle.INPUT_BORDER,
                       focuscolor=DarkWalletStyle.INPUT_FOCUS,
                       foreground=DarkWalletStyle.TEXT_PRIMARY,
                       insertcolor=DarkWalletStyle.TEXT_PRIMARY,
                       padding=8,
                       relief="flat")
        
        style.map("TEntry",
                 bordercolor=[('focus', DarkWalletStyle.INPUT_FOCUS)])
        
        # 设置标签样式
        style.configure("TLabel",
                       background=DarkWalletStyle.BG_COLOR,
                       foreground=DarkWalletStyle.TEXT_PRIMARY)
        
        # 设置标题标签样式
        style.configure("Title.TLabel",
                       background=DarkWalletStyle.BG_COLOR,
                       foreground=DarkWalletStyle.PRIMARY_LIGHT,
                       font=DarkWalletStyle.FONT_LARGE)
        
        # 设置复选框样式
        style.configure("TCheckbutton",
                       background=DarkWalletStyle.BG_COLOR,
                       foreground=DarkWalletStyle.TEXT_PRIMARY,
                       focuscolor=DarkWalletStyle.PRIMARY)
        
        # 设置进度条样式
        style.configure("TProgressbar",
                       background=DarkWalletStyle.PRIMARY,
                       troughcolor=DarkWalletStyle.BG_SECONDARY,
                       borderwidth=0,
                       lightcolor=DarkWalletStyle.PRIMARY,
                       darkcolor=DarkWalletStyle.PRIMARY)
        
        # 设置面板样式
        style.configure("TFrame",
                       background=DarkWalletStyle.BG_COLOR,
                       borderwidth=0)
        
        # 设置卡片面板样式
        style.configure("Card.TFrame",
                       background=DarkWalletStyle.CARD_BG,
                       borderwidth=1,
                       relief="solid",
                       bordercolor=DarkWalletStyle.BORDER_COLOR)
        
        root.configure(bg=DarkWalletStyle.BG_COLOR)


class DarkButton(tk.Canvas):
    """暗色主题按钮实现 - 仿WPF Dark Wallet Payment风格"""
    
    def __init__(self, master, text="", command=None, width=120, height=36, 
                 bg_color=None, hover_color=None, active_color=None,
                 text_color=None, radius=6, button_type="primary", **kwargs):
        # 获取背景色，ttk组件需要特殊处理
        try:
            bg = master["bg"]
        except (tk.TclError, KeyError):
            bg = DarkWalletStyle.BG_COLOR
            
        # 根据按钮类型设置默认颜色
        if button_type == "primary":
            bg_color = bg_color or DarkWalletStyle.BUTTON_BG
            hover_color = hover_color or DarkWalletStyle.BUTTON_HOVER
            active_color = active_color or DarkWalletStyle.BUTTON_ACTIVE
            text_color = text_color or DarkWalletStyle.TEXT_PRIMARY
        elif button_type == "secondary":
            bg_color = bg_color or DarkWalletStyle.BUTTON_SECONDARY
            hover_color = hover_color or DarkWalletStyle.BUTTON_SECONDARY_HOVER
            active_color = active_color or DarkWalletStyle.SECONDARY
            text_color = text_color or DarkWalletStyle.TEXT_PRIMARY
        else:
            bg_color = bg_color or DarkWalletStyle.BUTTON_BG
            hover_color = hover_color or DarkWalletStyle.BUTTON_HOVER
            active_color = active_color or DarkWalletStyle.BUTTON_ACTIVE
            text_color = text_color or DarkWalletStyle.TEXT_PRIMARY
            
        super().__init__(master, width=width, height=height, 
                         highlightthickness=0, bg=bg, bd=0, **kwargs)
        self.bg_color = bg_color
        self.hover_color = hover_color
        self.active_color = active_color
        self.text_color = text_color
        self.command = command
        self.radius = radius
        self.state = "normal"
        
        # 创建按钮和文本
        self.button_id = self.create_rounded_rect(0, 0, width, height, radius, fill=bg_color, outline="")
        self.text_id = self.create_text(width//2, height//2, text=text, fill=text_color, font=DarkWalletStyle.FONT)
        
        # 绑定事件
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<ButtonPress-1>", self._on_press)
        self.bind("<ButtonRelease-1>", self._on_release)
    
    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        """创建圆角矩形"""
        points = [
            x1+radius, y1,
            x2-radius, y1,
            x2, y1,
            x2, y1+radius,
            x2, y2-radius,
            x2, y2,
            x2-radius, y2,
            x1+radius, y2,
            x1, y2,
            x1, y2-radius,
            x1, y1+radius,
            x1, y1
        ]
        return self.create_polygon(points, **kwargs, smooth=True)
    
    def _on_enter(self, event):
        if self.state != "disabled":
            self.itemconfig(self.button_id, fill=self.hover_color)
            self.configure(cursor="hand2")
    
    def _on_leave(self, event):
        if self.state != "disabled":
            self.itemconfig(self.button_id, fill=self.bg_color)
            self.configure(cursor="")
    
    def _on_press(self, event):
        if self.state != "disabled":
            self.itemconfig(self.button_id, fill=self.active_color)
    
    def _on_release(self, event):
        if self.state != "disabled":
            self.itemconfig(self.button_id, fill=self.hover_color)
            if self.command:
                self.command()
    
    def config(self, **kwargs):
        if "state" in kwargs:
            self.state = kwargs["state"]
            if kwargs["state"] == "disabled":
                self.itemconfig(self.button_id, fill=DarkWalletStyle.TEXT_DISABLED)
                self.itemconfig(self.text_id, fill=DarkWalletStyle.TEXT_MUTED)
            else:
                self.itemconfig(self.button_id, fill=self.bg_color)
                self.itemconfig(self.text_id, fill=self.text_color)
        if "text" in kwargs:
            self.itemconfig(self.text_id, text=kwargs["text"])


class SpriteSheetToGIFApp(tk.Tk):
    """暗色钱包风格的精灵图集转换器 - 仿WPF Dark Wallet Payment"""

    def __init__(self):
        super().__init__()
        self.title("精灵图集转换器 - Dark Wallet Style")
        self.resizable(False, False)
        self.conversion_running = False

        # 配置文件路径
        self.config_file = os.path.join(os.path.expanduser("~"), ".sprite_converter_config.json")

        # 应用暗色钱包主题
        DarkWalletStyle.apply_theme(self)

        # 配置主窗口
        self.configure(bg=DarkWalletStyle.BG_COLOR)
        self.padx = DarkWalletStyle.PADDING
        self.pady = DarkWalletStyle.PADDING

        # 设置窗口最小尺寸
        self.minsize(600, 500)

        # 创建图标
        try:
            app_icon = Image.new('RGBA', (32, 32), DarkWalletStyle.PRIMARY)
            self.app_icon = ImageTk.PhotoImage(app_icon)
            self.iconphoto(True, self.app_icon)
        except:
            pass

        self._build_widgets()

        # 加载配置
        self._load_config()

        # 设置拖放支持
        self._setup_drop()

    # ---------------------------------------------------------------------
    # 日志配置
    # ---------------------------------------------------------------------
    logging.basicConfig(
        level=logging.DEBUG,  # 如需减少输出可改为 INFO
        format="[%(levelname)s] %(asctime)s — %(message)s",
        datefmt="%H:%M:%S",
    )

    # ---------------------------------------------------------------------
    # 配置文件处理
    # ---------------------------------------------------------------------
    def _load_config(self):
        """加载之前的配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 恢复尺寸设置
                if 'frame_width' in config:
                    self.frame_width_var.set(str(config['frame_width']))
                if 'frame_height' in config:
                    self.frame_height_var.set(str(config['frame_height']))
                if 'spacing' in config:
                    self.spacing_var.set(str(config['spacing']))
                if 'duration' in config:
                    self.duration_var.set(str(config['duration']))
                if 'alpha_threshold' in config:
                    self.alpha_thresh_var.set(str(config['alpha_threshold']))
                if 'use_threading' in config:
                    self.use_threading_var.set(config['use_threading'])

                # 恢复上次的目录
                if 'last_dir' in config:
                    self.last_dir = config['last_dir']
                else:
                    self.last_dir = os.path.expanduser("~")

                logging.debug("已加载配置: %s", config)
        except Exception as e:
            logging.debug("加载配置失败: %s", e)
            self.last_dir = os.path.expanduser("~")

    def _save_config(self):
        """保存当前配置"""
        try:
            config = {
                'frame_width': int(self.frame_width_var.get()),
                'frame_height': int(self.frame_height_var.get()),
                'spacing': int(self.spacing_var.get()),
                'duration': int(self.duration_var.get()),
                'alpha_threshold': int(self.alpha_thresh_var.get()),
                'use_threading': self.use_threading_var.get(),
                'last_dir': self.last_dir
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)

            logging.debug("已保存配置: %s", config)
        except Exception as e:
            logging.debug("保存配置失败: %s", e)

    # ---------------------------------------------------------------------
    # 拖放支持
    # ---------------------------------------------------------------------
    def _setup_drop(self):
        """设置文件拖放支持"""
        # 暂时禁用拖放功能，避免依赖问题
        logging.debug("拖放功能已禁用（避免依赖问题）")
        return

        # 以下代码保留，如果需要拖放功能可以取消注释
        # try:
        #     import tkinterdnd2
        #     self.drop_target_register(tkinterdnd2.DND_FILES)
        #     self.dnd_bind('<<Drop>>', self._handle_drop)
        #     logging.debug("已启用拖放支持")
        # except ImportError:
        #     logging.debug("无法启用拖放支持：缺少 tkinterdnd2")

    def _handle_drop(self, event):
        """处理拖放事件"""
        try:
            # 获取拖放的文件路径
            file_path = event.data

            # Windows中可能有{}包围，需要去除
            if file_path.startswith('{') and file_path.endswith('}'):
                file_path = file_path[1:-1]

            # 检查是否为PNG文件
            if file_path.lower().endswith('.png'):
                self.input_path_var.set(file_path)
                default_output = os.path.splitext(file_path)[0] + ".webp"
                self.output_path_var.set(default_output)

                # 更新上次使用的目录
                self.last_dir = os.path.dirname(file_path)

                logging.debug("已接受拖放文件: %s", file_path)
        except Exception as e:
            logging.debug("处理拖放失败: %s", e)

    def _handle_drop_fallback(self, event):
        """备用拖放处理方法，用于不同平台"""
        try:
            # 尝试获取文件路径
            file_path = event.data if hasattr(event, 'data') else str(event)

            # 提取文件路径的不同尝试
            if file_path.startswith(('file:', '{file:')):
                file_path = file_path.replace('file:', '').replace('{', '').replace('}', '')

            # 检查是否为PNG文件
            if file_path.lower().endswith('.png'):
                self.input_path_var.set(file_path)
                default_output = os.path.splitext(file_path)[0] + ".webp"
                self.output_path_var.set(default_output)

                # 更新上次使用的目录
                self.last_dir = os.path.dirname(file_path)

                logging.debug("已接受拖放文件: %s", file_path)
        except Exception as e:
            logging.debug("处理拖放失败: %s", e)

    # ---------------------------------------------------------------------
    # GUI 构建
    # ---------------------------------------------------------------------
    def _build_widgets(self):
        # 创建主框架 - 使用卡片样式
        self.main_frame = ttk.Frame(self, style="Card.TFrame", padding=(DarkWalletStyle.CARD_PADDING, DarkWalletStyle.CARD_PADDING))
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=DarkWalletStyle.PADDING, pady=DarkWalletStyle.PADDING)

        # 标题区域
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = ttk.Label(header_frame, text="精灵图集转换器",
                               style="Title.TLabel")
        title_label.pack(side=tk.LEFT)

        # 副标题
        subtitle_label = ttk.Label(header_frame, text="Dark Wallet Style",
                                 foreground=DarkWalletStyle.TEXT_SECONDARY,
                                 font=DarkWalletStyle.FONT_SMALL)
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # 创建内容框架
        content_frame = ttk.Frame(self.main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域 - 使用卡片样式
        file_card = ttk.Frame(content_frame, style="Card.TFrame", padding=(DarkWalletStyle.PADDING, DarkWalletStyle.PADDING))
        file_card.pack(fill=tk.X, pady=(0, 15))

        # 输入文件
        input_frame = ttk.Frame(file_card)
        input_frame.pack(fill=tk.X, pady=(0, 12))

        ttk.Label(input_frame, text="输入 PNG:", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.input_path_var = tk.StringVar()
        input_entry = ttk.Entry(input_frame, textvariable=self.input_path_var, width=48)
        input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_btn = DarkButton(input_frame, text="浏览...", command=self._select_input,
                              width=80, height=DarkWalletStyle.ENTRY_HEIGHT, button_type="secondary")
        browse_btn.pack(side=tk.LEFT)

        # 输出文件
        output_frame = ttk.Frame(file_card)
        output_frame.pack(fill=tk.X)

        ttk.Label(output_frame, text="输出 WebP:", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=48)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        save_btn = DarkButton(output_frame, text="选择...", command=self._select_output,
                            width=80, height=DarkWalletStyle.ENTRY_HEIGHT, button_type="secondary")
        save_btn.pack(side=tk.LEFT)

        # 参数设置区域 - 使用卡片样式
        params_card = ttk.Frame(content_frame, style="Card.TFrame", padding=(DarkWalletStyle.PADDING, DarkWalletStyle.PADDING))
        params_card.pack(fill=tk.X, pady=(0, 15))

        # 参数标题
        params_title = ttk.Label(params_card, text="转换参数",
                               foreground=DarkWalletStyle.PRIMARY_LIGHT,
                               font=DarkWalletStyle.FONT_BOLD)
        params_title.pack(anchor=tk.W, pady=(0, 10))

        # 参数网格容器
        params_grid = ttk.Frame(params_card)
        params_grid.pack(fill=tk.X)

        # 左侧参数列
        left_params = ttk.Frame(params_grid)
        left_params.pack(side=tk.LEFT, fill=tk.Y, expand=True)

        # 帧宽度
        width_frame = ttk.Frame(left_params)
        width_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(width_frame, text="帧宽度:", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.frame_width_var = tk.StringVar(value="64")
        width_entry = ttk.Entry(width_frame, textvariable=self.frame_width_var, width=12)
        width_entry.pack(side=tk.LEFT)

        # 帧高度
        height_frame = ttk.Frame(left_params)
        height_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(height_frame, text="帧高度:", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.frame_height_var = tk.StringVar(value="64")
        height_entry = ttk.Entry(height_frame, textvariable=self.frame_height_var, width=12)
        height_entry.pack(side=tk.LEFT)

        # 右侧参数列
        right_params = ttk.Frame(params_grid)
        right_params.pack(side=tk.RIGHT, fill=tk.Y, expand=True, padx=(30, 0))

        # 帧间距
        spacing_frame = ttk.Frame(right_params)
        spacing_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(spacing_frame, text="间距(px):", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.spacing_var = tk.StringVar(value="0")
        spacing_entry = ttk.Entry(spacing_frame, textvariable=self.spacing_var, width=12)
        spacing_entry.pack(side=tk.LEFT)

        # 帧时长
        duration_frame = ttk.Frame(right_params)
        duration_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(duration_frame, text="帧时长(ms):", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.duration_var = tk.StringVar(value="100")
        duration_entry = ttk.Entry(duration_frame, textvariable=self.duration_var, width=12)
        duration_entry.pack(side=tk.LEFT)

        # Alpha 阈值 - 跨列显示
        alpha_frame = ttk.Frame(params_card)
        alpha_frame.pack(fill=tk.X, pady=(10, 0))
        ttk.Label(alpha_frame, text="透明阈值:", foreground=DarkWalletStyle.TEXT_SECONDARY).pack(side=tk.LEFT, padx=(0, 10))
        self.alpha_thresh_var = tk.StringVar(value="0")
        alpha_entry = ttk.Entry(alpha_frame, textvariable=self.alpha_thresh_var, width=12)
        alpha_entry.pack(side=tk.LEFT)
        ttk.Label(alpha_frame, text="(0-255, 0为不处理透明度)",
                 foreground=DarkWalletStyle.TEXT_MUTED,
                 font=DarkWalletStyle.FONT_SMALL).pack(side=tk.LEFT, padx=(10, 0))

        # 控制区域 - 使用卡片样式
        control_card = ttk.Frame(content_frame, style="Card.TFrame", padding=(DarkWalletStyle.PADDING, DarkWalletStyle.PADDING))
        control_card.pack(fill=tk.X, pady=(0, 15))

        # 控制区域布局
        control_layout = ttk.Frame(control_card)
        control_layout.pack(fill=tk.X)

        # 左侧选项
        options_frame = ttk.Frame(control_layout)
        options_frame.pack(side=tk.LEFT, fill=tk.Y)

        self.use_threading_var = tk.BooleanVar(value=True)
        thread_check = ttk.Checkbutton(options_frame, text="使用多线程加速",
                                     variable=self.use_threading_var)
        thread_check.pack(side=tk.TOP, anchor=tk.W)

        # 右侧按钮
        buttons_frame = ttk.Frame(control_layout)
        buttons_frame.pack(side=tk.RIGHT, fill=tk.Y)

        self.convert_btn = DarkButton(buttons_frame, text="开始转换", command=self._start_conversion,
                                    width=140, height=DarkWalletStyle.BUTTON_HEIGHT, button_type="primary")
        self.convert_btn.pack(side=tk.RIGHT)

        # 进度区域 - 使用卡片样式
        progress_card = ttk.Frame(self.main_frame, style="Card.TFrame", padding=(DarkWalletStyle.PADDING, DarkWalletStyle.PADDING))
        progress_card.pack(fill=tk.X, pady=(0, 10))

        # 进度标题
        progress_title = ttk.Label(progress_card, text="转换进度",
                                 foreground=DarkWalletStyle.PRIMARY_LIGHT,
                                 font=DarkWalletStyle.FONT_BOLD)
        progress_title.pack(anchor=tk.W, pady=(0, 8))

        # 进度条
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_bar = ttk.Progressbar(progress_card, variable=self.progress_var,
                                          style="TProgressbar", length=100, mode="determinate")
        self.progress_bar.pack(fill=tk.X, pady=(0, 8))

        # 状态标签
        self.status_label = ttk.Label(progress_card, text="就绪 - 拖放PNG文件或点击浏览按钮开始",
                                    foreground=DarkWalletStyle.TEXT_SECONDARY)
        self.status_label.pack(anchor=tk.W)

        # 底部提示区域
        hint_frame = ttk.Frame(self.main_frame)
        hint_frame.pack(fill=tk.X, pady=(5, 0))

        # 拖放提示标签
        drop_hint = ttk.Label(hint_frame,
                             text="💡 提示: 您可以直接将PNG文件拖放到此窗口中",
                             foreground=DarkWalletStyle.TEXT_MUTED,
                             font=DarkWalletStyle.FONT_SMALL)
        drop_hint.pack(anchor=tk.W)

        # 快捷键提示
        shortcut_hint = ttk.Label(hint_frame,
                                text="⌨️ 快捷键: Ctrl+O 选择文件 | Ctrl+S 保存位置 | F5 开始转换",
                                foreground=DarkWalletStyle.TEXT_MUTED,
                                font=DarkWalletStyle.FONT_SMALL)
        shortcut_hint.pack(anchor=tk.W, pady=(2, 0))

        # 设置快捷键
        self.bind("<Control-o>", lambda event: self._select_input())
        self.bind("<Control-s>", lambda event: self._select_output())
        self.bind("<F5>", lambda event: self._start_conversion())

    # ---------------------------------------------------------------------
    # 事件处理
    # ---------------------------------------------------------------------
    def _select_input(self):
        path = filedialog.askopenfilename(
            filetypes=[("PNG 图片", "*.png")],
            initialdir=getattr(self, 'last_dir', os.path.expanduser("~"))
        )
        if path:
            self.input_path_var.set(path)
            default_output = os.path.splitext(path)[0] + ".webp"
            # 若用户尚未自定义输出路径，则自动填充
            if not self.output_path_var.get():
                self.output_path_var.set(default_output)

            # 更新上次使用的目录
            self.last_dir = os.path.dirname(path)
            self._save_config()

    def _select_output(self):
        path = filedialog.asksaveasfilename(
            defaultextension=".webp",
            filetypes=[("WebP 图片", "*.webp")],
            initialdir=getattr(self, 'last_dir', os.path.expanduser("~"))
        )
        if path:
            self.output_path_var.set(path)

            # 更新上次使用的目录
            self.last_dir = os.path.dirname(path)
            self._save_config()

    def _start_conversion(self):
        """在单独线程中启动转换以保持UI响应"""
        if self.conversion_running:
            return

        self.conversion_running = True
        self.convert_btn.config(state="disabled")
        self._update_ui("转换中...", progress=0)

        conversion_thread = threading.Thread(target=self._convert)
        conversion_thread.daemon = True
        conversion_thread.start()

    def _update_ui(self, message, is_error=False, progress=None):
        """线程安全地更新UI"""
        color = DarkWalletStyle.DANGER if is_error else DarkWalletStyle.TEXT_SECONDARY
        self.status_label.config(text=message, foreground=color)

        if progress is not None:
            self.progress_var.set(progress)

        if not is_error and progress == 100:
            self.conversion_running = False
            self.convert_btn.config(state="normal")

    def _process_frame(self, frame, alpha_threshold):
        """处理单个帧"""
        if alpha_threshold > 0:
            rgba = frame.convert("RGBA")
            pixdata = rgba.getdata()

            # 使用列表推导式优化处理透明度
            new_data = [(r, g, b, 255 if a >= alpha_threshold else 0)
                        for r, g, b, a in pixdata]
            rgba.putdata(new_data)
            return rgba
        return frame

    def _convert(self):
        """执行 PNG → WebP 转换。"""
        input_path = self.input_path_var.get()
        output_path = self.output_path_var.get()
        use_threading = self.use_threading_var.get()

        logging.info("开始转换: %s -> %s", input_path, output_path)

        # 参数校验
        try:
            frame_w = int(self.frame_width_var.get())
            frame_h = int(self.frame_height_var.get())
            spacing = int(self.spacing_var.get())
            alpha_threshold = int(self.alpha_thresh_var.get())
            duration = int(self.duration_var.get())

            # 保存当前配置
            self._save_config()
        except ValueError:
            self.after(0, lambda: self._update_ui("错误: 请确保所有参数都是有效整数", True))
            messagebox.showerror("错误", "请确保帧宽、高、间距、时长及阈值均为整数。")
            return

        if frame_w <= 0 or frame_h <= 0 or duration <= 0 or spacing < 0:
            self.after(0, lambda: self._update_ui("错误: 参数值无效", True))
            messagebox.showerror("错误", "帧宽度、高度、时长必须为正数，间距不能为负数。")
            return

        if not (0 <= alpha_threshold <= 255):
            self.after(0, lambda: self._update_ui("错误: 透明阈值必须在 0-255 之间", True))
            messagebox.showerror("错误", "透明阈值必须在 0-255 之间。")
            return

        if not os.path.isfile(input_path):
            self.after(0, lambda: self._update_ui("错误: 输入文件不存在", True))
            messagebox.showerror("错误", "输入文件不存在或路径无效。")
            return

        # 加载精灵图
        try:
            self.after(0, lambda: self._update_ui("正在加载图像...", progress=5))
            sprite_sheet = Image.open(input_path)
            sprite_sheet.load()
            logging.info("已加载图像: 分辨率=%dx%d", *sprite_sheet.size)
        except Exception as exc:
            self.after(0, lambda: self._update_ui(f"错误: {exc}", True))
            messagebox.showerror("错误", f"无法打开图像文件: {exc}")
            return

        sheet_w, sheet_h = sprite_sheet.size

        # 预计算切片坐标以优化循环
        self.after(0, lambda: self._update_ui("计算切片坐标...", progress=10))
        frame_coords = []
        y = 0
        while y + frame_h <= sheet_h:
            x = 0
            while x + frame_w <= sheet_w:
                frame_coords.append((x, y, x + frame_w, y + frame_h))
                x += frame_w + spacing
            y += frame_h + spacing

        if not frame_coords:
            self.after(0, lambda: self._update_ui("错误: 未能计算出任何帧", True))
            messagebox.showerror("错误", "未能切割出任何帧，请检查输入尺寸/间距。")
            return

        self.after(0, lambda: self._update_ui(f"开始处理 {len(frame_coords)} 帧...", progress=15))

        # 处理帧
        frames = []

        if use_threading and len(frame_coords) > 10:
            try:
                img_array = sprite_sheet.convert('RGBA').tobytes()
                img_size = sprite_sheet.size
                img_mode = 'RGBA'

                with ThreadPoolExecutor(max_workers=min(os.cpu_count() or 2, 4)) as executor:
                    futures = []
                    batch_size = 10
                    for i in range(0, len(frame_coords), batch_size):
                        batch = frame_coords[i:i+batch_size]
                        futures.append(executor.submit(
                            self._process_frame_batch_safe,
                            img_array, img_size, img_mode, batch, alpha_threshold
                        ))
                        progress = min(80, 15 + int((i / len(frame_coords)) * 65))
                        self.after(0, lambda p=progress: self._update_ui(f"处理帧: {int((p-15)/65*100)}%...", progress=p))

                    for future in futures:
                        frames.extend(future.result())
            except Exception as e:
                logging.error("多线程处理失败，回退到单线程: %s", str(e))
                frames = []
                use_threading = False

        if not use_threading:
            self.after(0, lambda: self._update_ui("使用单线程处理...", progress=15))
            for i, coord in enumerate(frame_coords):
                if i % 10 == 0:
                    progress = min(80, 15 + int((i / len(frame_coords)) * 65))
                    self.after(0, lambda p=progress: self._update_ui(f"处理帧: {int((p-15)/65*100)}%...", progress=p))

                frame = sprite_sheet.crop(coord)
                if alpha_threshold > 0:
                    frame = self._process_frame(frame, alpha_threshold)
                frames.append(frame.convert("RGBA"))

        logging.info("共切割出 %d 帧", len(frames))

        if not frames:
            self.after(0, lambda: self._update_ui("错误: 未能处理出任何帧", True))
            messagebox.showerror("错误", "未能切割出任何帧，请检查输入尺寸/间距。")
            return

        # 保存 WebP
        try:
            self.after(0, lambda: self._update_ui("正在保存WebP...", progress=85))
            logging.info("开始保存 WebP，帧数=%d…", len(frames))

            start_encode = time.time()

            frames[0].save(
                output_path,
                save_all=True,
                append_images=frames[1:],
                duration=duration,
                loop=0,
                format="WEBP",
                lossless=True,
                method=6,
                reductions=6,
            )

            encoding_time = time.time() - start_encode
            logging.info("WebP 保存完成! 用时 %.2fs", encoding_time)

            # 清理内存
            for frame in frames:
                frame.close()
            sprite_sheet.close()

            self.after(0, lambda: self._update_ui(f"完成! 用时 {encoding_time:.1f}秒", progress=100))
            messagebox.showinfo("完成", f"WebP 已成功保存至:\n{output_path}")
            logging.info("全部流程完成，输出文件: %s", output_path)

        except Exception as exc:
            logging.exception("保存 WebP 失败")
            self.after(0, lambda: self._update_ui(f"保存失败: {exc}", True))
            messagebox.showerror("错误", f"保存 WebP 失败: {exc}")
            return

        finally:
            self.after(0, lambda: self._update_ui("就绪 - 拖放PNG文件或点击浏览按钮开始", progress=0))
            self.after(0, lambda: self.convert_btn.config(state="normal"))
            self.conversion_running = False

    def _process_frame_batch_safe(self, img_bytes, img_size, img_mode, coords_batch, alpha_threshold):
        """使用字节数据处理批量帧，线程安全"""
        frames = []
        img = Image.frombytes(img_mode, img_size, img_bytes)

        for coord in coords_batch:
            frame = img.crop(coord)
            if alpha_threshold > 0:
                frame = self._process_frame(frame, alpha_threshold)
            frames.append(frame.convert("RGBA"))
        return frames


if __name__ == "__main__":
    # 检测 Pillow 是否安装完整，给出友好提示
    try:
        from PIL import Image, ImageTk  # 更具体的导入
    except ImportError:
        tk.messagebox.showerror(
            "依赖缺失", "未检测到 Pillow，请先运行:\npip install Pillow"
        )
        raise SystemExit(1)

    # 直接启动应用，不使用拖放功能（避免依赖问题）
    try:
        app = SpriteSheetToGIFApp()
        app.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        tk.messagebox.showerror("错误", f"程序启动失败: {e}")
        raise SystemExit(1)
