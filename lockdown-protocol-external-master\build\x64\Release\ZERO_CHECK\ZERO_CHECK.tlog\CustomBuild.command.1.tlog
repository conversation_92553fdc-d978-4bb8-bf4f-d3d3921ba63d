^D:\ALONE\STATIC\LOCKDOWN-PROTOCOL-EXTERNAL-MASTER\BUILD\CMAKEFILES\3DF5A6436F93606B286A05E1D95713E7\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/alone/static/lockdown-protocol-external-master -BD:/alone/static/lockdown-protocol-external-master/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/alone/static/lockdown-protocol-external-master/build/protocol.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
