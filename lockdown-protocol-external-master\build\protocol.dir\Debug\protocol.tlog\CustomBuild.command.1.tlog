^D:\ALONE\STATIC\LOCKDOWN-PROTOCOL-EXTERNAL-MASTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/alone/static/lockdown-protocol-external-master -BD:/alone/static/lockdown-protocol-external-master/build --check-stamp-file D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
