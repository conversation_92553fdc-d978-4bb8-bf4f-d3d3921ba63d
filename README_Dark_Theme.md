# 精灵图集转换器 - 暗色钱包主题版本

## 概述

这是一个仿照 WPF Dark Wallet Payment 界面风格设计的 Python GUI 应用程序，用于将精灵图集（PNG）转换为 WebP 动画。采用现代暗色主题设计，提供专业的用户体验。

## 主要特性

### 🎨 暗色主题设计
- **深色背景**: 主背景使用 `#1e1e1e`，营造专业的暗色环境
- **卡片式布局**: 使用 `#252526` 的卡片背景，清晰分隔功能区域
- **现代蓝色主色调**: 使用 `#007acc` 作为主色，体现现代科技感
- **层次化文本颜色**: 
  - 主要文本: `#ffffff`
  - 次要文本: `#cccccc`
  - 提示文本: `#999999`

### 🖱️ 交互体验
- **自定义按钮**: 圆角设计，悬停和点击效果
- **智能输入框**: 暗色背景，蓝色焦点边框
- **进度反馈**: 实时显示转换进度和状态
- **拖放支持**: 直接拖放 PNG 文件到窗口

### ⚡ 功能特性
- **多线程处理**: 支持多线程加速转换
- **参数记忆**: 自动保存和恢复用户设置
- **快捷键支持**: 
  - `Ctrl+O`: 选择输入文件
  - `Ctrl+S`: 选择输出位置
  - `F5`: 开始转换

## 界面设计对比

### WPF Dark Wallet Payment 风格特点
1. **深色主题**: 使用深灰色和黑色作为主要背景
2. **卡片式设计**: 功能区域使用卡片容器分组
3. **现代蓝色**: 使用蓝色作为主色调和强调色
4. **扁平化设计**: 简洁的按钮和控件设计
5. **层次化布局**: 清晰的信息层次和视觉引导

### Python 实现的设计元素

#### 颜色方案
```python
# 主背景色系
BG_COLOR = "#1e1e1e"          # 主背景
CARD_BG = "#252526"           # 卡片背景
BG_SECONDARY = "#2d2d30"      # 次要背景

# 主色调
PRIMARY = "#007acc"           # 主蓝色
PRIMARY_LIGHT = "#4fc3f7"     # 浅蓝色
PRIMARY_HOVER = "#1f9bcf"     # 悬停蓝色

# 文本颜色
TEXT_PRIMARY = "#ffffff"      # 主要文本
TEXT_SECONDARY = "#cccccc"    # 次要文本
TEXT_MUTED = "#999999"        # 提示文本
```

#### 组件设计
- **DarkButton**: 自定义圆角按钮，支持悬停效果
- **卡片布局**: 使用 `Card.TFrame` 样式创建卡片容器
- **现代字体**: 使用 Segoe UI 字体系列
- **圆角设计**: 6px 圆角半径，符合现代设计趋势

## 使用方法

### 1. 运行程序
```bash
python sprite_sheet_to_gif_gui_dark.py
```

### 2. 选择文件
- 点击"浏览..."按钮选择 PNG 精灵图集
- 或直接拖放 PNG 文件到窗口中

### 3. 设置参数
- **帧宽度/高度**: 单个帧的像素尺寸
- **间距**: 帧之间的像素间距
- **帧时长**: 每帧显示时间（毫秒）
- **透明阈值**: 透明度处理阈值（0-255）

### 4. 开始转换
- 点击"开始转换"按钮或按 F5
- 实时查看转换进度
- 转换完成后会显示成功提示

## 技术实现

### 主要技术栈
- **GUI框架**: Tkinter + ttk
- **图像处理**: Pillow (PIL)
- **多线程**: ThreadPoolExecutor
- **配置管理**: JSON

### 核心类设计

#### DarkWalletStyle
统一管理暗色主题的颜色、字体和尺寸定义：
```python
class DarkWalletStyle:
    # 颜色定义
    BG_COLOR = "#1e1e1e"
    PRIMARY = "#007acc"
    
    # 字体定义
    FONT = ("Segoe UI", 9)
    FONT_BOLD = ("Segoe UI", 9, "bold")
    
    # 尺寸定义
    BUTTON_HEIGHT = 36
    BORDER_RADIUS = 6
```

#### DarkButton
自定义按钮组件，实现圆角和交互效果：
```python
class DarkButton(tk.Canvas):
    def __init__(self, master, text="", button_type="primary"):
        # 根据按钮类型设置颜色
        # 创建圆角矩形
        # 绑定交互事件
```

### 界面布局结构
```
主窗口 (BG_COLOR)
├── 主框架 (Card.TFrame)
│   ├── 标题区域
│   ├── 文件选择卡片 (Card.TFrame)
│   ├── 参数设置卡片 (Card.TFrame)
│   ├── 控制区域卡片 (Card.TFrame)
│   └── 进度显示卡片 (Card.TFrame)
└── 底部提示区域
```

## 设计原则

### 1. 视觉层次
- 使用不同的背景色创建层次感
- 主要操作使用鲜明的蓝色突出显示
- 次要信息使用较暗的颜色

### 2. 用户体验
- 清晰的功能分组
- 直观的操作流程
- 及时的状态反馈

### 3. 现代设计
- 扁平化设计语言
- 圆角和阴影效果
- 一致的间距和对齐

## 与原版对比

| 特性 | 原版 (FlatStyle) | 暗色版 (DarkWalletStyle) |
|------|------------------|---------------------------|
| 背景色 | 浅色 (#f8f9fa) | 深色 (#1e1e1e) |
| 主色调 | 蓝色 (#0078d7) | 现代蓝 (#007acc) |
| 按钮设计 | 标准按钮 | 自定义圆角按钮 |
| 布局方式 | 简单分组 | 卡片式布局 |
| 视觉效果 | 基础扁平 | 现代暗色主题 |

## 依赖要求

```
Pillow>=8.0.0
tkinter (Python 内置)
```

## 文件说明

- `sprite_sheet_to_gif_gui_dark.py`: 暗色主题版本主程序
- `sprite_sheet_to_gif_gui.py`: 原版程序
- `README_Dark_Theme.md`: 本说明文档

通过这个暗色主题版本，你的 Python 程序现在具有了类似 WPF Dark Wallet Payment 的专业外观和现代用户体验！
