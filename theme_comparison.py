#!/usr/bin/env python3
"""
主题对比工具 - 展示原版和暗色主题版本的差异
"""

import tkinter as tk
from tkinter import ttk
import subprocess
import sys
import os

class ThemeComparison:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("精灵图集转换器 - 主题对比")
        self.root.geometry("800x600")
        self.root.configure(bg="#f0f0f0")
        
        self.create_widgets()
    
    def create_widgets(self):
        # 标题
        title_frame = tk.Frame(self.root, bg="#f0f0f0")
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = tk.Label(title_frame, 
                              text="精灵图集转换器 - 主题对比", 
                              font=("Arial", 18, "bold"),
                              bg="#f0f0f0")
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, 
                                 text="选择要运行的版本进行对比", 
                                 font=("Arial", 12),
                                 bg="#f0f0f0",
                                 fg="#666666")
        subtitle_label.pack(pady=(5, 0))
        
        # 对比区域
        comparison_frame = tk.Frame(self.root, bg="#f0f0f0")
        comparison_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # 原版
        original_frame = tk.Frame(comparison_frame, bg="white", relief=tk.RAISED, bd=2)
        original_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(original_frame, text="原版 (浅色主题)", 
                font=("Arial", 14, "bold"), bg="white").pack(pady=15)
        
        # 原版特性
        original_features = [
            "• 浅色背景 (#f8f9fa)",
            "• 标准蓝色主题 (#0078d7)", 
            "• 基础扁平化设计",
            "• 简单布局分组",
            "• 标准 ttk 组件"
        ]
        
        for feature in original_features:
            tk.Label(original_frame, text=feature, 
                    font=("Arial", 10), bg="white", 
                    anchor=tk.W).pack(fill=tk.X, padx=20, pady=2)
        
        # 原版按钮
        tk.Button(original_frame, text="运行原版", 
                 command=self.run_original,
                 bg="#0078d7", fg="white",
                 font=("Arial", 12, "bold"),
                 pady=10).pack(pady=20)
        
        # 暗色版
        dark_frame = tk.Frame(comparison_frame, bg="#1e1e1e", relief=tk.RAISED, bd=2)
        dark_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        tk.Label(dark_frame, text="暗色版 (WPF Dark Wallet Style)", 
                font=("Arial", 14, "bold"), bg="#1e1e1e", fg="white").pack(pady=15)
        
        # 暗色版特性
        dark_features = [
            "• 深色背景 (#1e1e1e)",
            "• 现代蓝色主题 (#007acc)",
            "• 卡片式布局设计", 
            "• 自定义圆角按钮",
            "• 专业暗色主题"
        ]
        
        for feature in dark_features:
            tk.Label(dark_frame, text=feature, 
                    font=("Arial", 10), bg="#1e1e1e", fg="#cccccc",
                    anchor=tk.W).pack(fill=tk.X, padx=20, pady=2)
        
        # 暗色版按钮
        dark_btn = tk.Frame(dark_frame, bg="#007acc", relief=tk.RAISED, bd=1)
        dark_btn.pack(pady=20)
        
        tk.Label(dark_btn, text="运行暗色版", 
                font=("Arial", 12, "bold"),
                bg="#007acc", fg="white",
                pady=10, padx=20,
                cursor="hand2").pack()
        
        dark_btn.bind("<Button-1>", lambda e: self.run_dark())
        dark_btn.bind("<Enter>", lambda e: dark_btn.configure(bg="#1f9bcf"))
        dark_btn.bind("<Leave>", lambda e: dark_btn.configure(bg="#007acc"))
        
        # 底部说明
        info_frame = tk.Frame(self.root, bg="#f0f0f0")
        info_frame.pack(fill=tk.X, pady=20)
        
        info_text = """
💡 提示: 
• 两个版本功能完全相同，仅界面风格不同
• 暗色版本仿照 WPF Dark Wallet Payment 界面设计
• 可以同时运行两个版本进行对比
        """
        
        tk.Label(info_frame, text=info_text, 
                font=("Arial", 10), bg="#f0f0f0", fg="#666666",
                justify=tk.LEFT).pack(padx=40)
    
    def run_original(self):
        """运行原版程序"""
        try:
            subprocess.Popen([sys.executable, "sprite_sheet_to_gif_gui.py"])
            print("已启动原版程序")
        except Exception as e:
            print(f"启动原版程序失败: {e}")
    
    def run_dark(self):
        """运行暗色版程序"""
        try:
            subprocess.Popen([sys.executable, "sprite_sheet_to_gif_gui_dark.py"])
            print("已启动暗色版程序")
        except Exception as e:
            print(f"启动暗色版程序失败: {e}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = ThemeComparison()
    app.run()
