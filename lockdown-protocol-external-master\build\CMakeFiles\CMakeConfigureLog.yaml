
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/7/10 22:04:52銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:01.35
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "D:/dev/Python312/Scripts/lld-link.com"
      - "D:/dev/Python312/Scripts/lld-link.exe"
      - "D:/dev/Python312/Scripts/lld-link"
      - "D:/dev/Python312/lld-link.com"
      - "D:/dev/Python312/lld-link.exe"
      - "D:/dev/Python312/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Users/<USER>/lld-link.com"
      - "C:/Users/<USER>/lld-link.exe"
      - "C:/Users/<USER>/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/WireGuard/lld-link.com"
      - "C:/Program Files/WireGuard/lld-link.exe"
      - "C:/Program Files/WireGuard/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/lld-link.com"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/lld-link.exe"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "D:/dev/Python312/Scripts/mt.com"
      - "D:/dev/Python312/Scripts/mt.exe"
      - "D:/dev/Python312/Scripts/mt"
      - "D:/dev/Python312/mt.com"
      - "D:/dev/Python312/mt.exe"
      - "D:/dev/Python312/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Users/<USER>/mt.com"
      - "C:/Users/<USER>/mt.exe"
      - "C:/Users/<USER>/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/WireGuard/mt.com"
      - "C:/Program Files/WireGuard/mt.exe"
      - "C:/Program Files/WireGuard/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/mt.com"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/mt.exe"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-CXX.cmake:6 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "D:/dev/Python312/Scripts/"
      - "D:/dev/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Users/<USER>/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/WireGuard/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files/protocol/bin/"
      - "C:/Program Files/protocol/sbin/"
      - "C:/Program Files/protocol/"
    searched_directories:
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "D:/dev/Python312/Scripts/rc.com"
      - "D:/dev/Python312/Scripts/rc.exe"
      - "D:/dev/Python312/Scripts/rc"
      - "D:/dev/Python312/rc.com"
      - "D:/dev/Python312/rc.exe"
      - "D:/dev/Python312/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Users/<USER>/rc.com"
      - "C:/Users/<USER>/rc.exe"
      - "C:/Users/<USER>/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/WireGuard/rc.com"
      - "C:/Program Files/WireGuard/rc.exe"
      - "C:/Program Files/WireGuard/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/rc.com"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/rc.exe"
      - "C:/Users/<USER>/.cursor/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files/protocol/bin/rc.com"
      - "C:/Program Files/protocol/bin/rc.exe"
      - "C:/Program Files/protocol/bin/rc"
      - "C:/Program Files/protocol/sbin/rc.com"
      - "C:/Program Files/protocol/sbin/rc.exe"
      - "C:/Program Files/protocol/sbin/rc"
      - "C:/Program Files/protocol/rc.com"
      - "C:/Program Files/protocol/rc.exe"
      - "C:/Program Files/protocol/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\dev\\Python312\\Scripts\\"
        - "D:\\dev\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "c:\\Users\\<USER>\\Program Files\\nodejs\\"
        - "C:\\Program Files\\WireGuard\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts"
      CMAKE_INSTALL_PREFIX: "C:/Program Files/protocol"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files/protocol"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/CMakeScratch/TryCompile-imz2ac"
      binary: "D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/CMakeScratch/TryCompile-imz2ac"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/CMakeScratch/TryCompile-imz2ac'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_49c5e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/10 22:04:54銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-imz2ac\\cmTC_49c5e.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_49c5e.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-imz2ac\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_49c5e.dir\\Debug\\cmTC_49c5e.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_49c5e.dir\\Debug\\cmTC_49c5e.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_49c5e.dir\\Debug\\cmTC_49c5e.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Fo"cmTC_49c5e.dir\\Debug\\\\" /Fd"cmTC_49c5e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Fo"cmTC_49c5e.dir\\Debug\\\\" /Fd"cmTC_49c5e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-imz2ac\\Debug\\cmTC_49c5e.exe" /INCREMENTAL /ILK:"cmTC_49c5e.dir\\Debug\\cmTC_49c5e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/CMakeScratch/TryCompile-imz2ac/Debug/cmTC_49c5e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/alone/static/lockdown-protocol-external-master/build/CMakeFiles/CMakeScratch/TryCompile-imz2ac/Debug/cmTC_49c5e.lib" /MACHINE:X64  /machine:x64 cmTC_49c5e.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_49c5e.vcxproj -> D:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-imz2ac\\Debug\\cmTC_49c5e.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_49c5e.dir\\Debug\\cmTC_49c5e.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_49c5e.dir\\Debug\\cmTC_49c5e.tlog\\cmTC_49c5e.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\alone\\static\\lockdown-protocol-external-master\\build\\CMakeFiles\\CMakeScratch\\TryCompile-imz2ac\\cmTC_49c5e.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.19
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
