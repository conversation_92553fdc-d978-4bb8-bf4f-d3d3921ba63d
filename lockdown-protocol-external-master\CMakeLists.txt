cmake_minimum_required(VERSION 3.20)

project(protocol LANGUAGES CXX)

# ── 通用编译选项 ───────────────────────────────
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# MSVC 多核编译
if (MSVC)
    add_compile_options(/MP)
endif()

# ── 源码列表 ──────────────────────────────────
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(IMGUI_DIR ${SRC_DIR}/overlay/imgui)
set(DX11_DIR  ${SRC_DIR}/overlay/dx11)

set(SOURCES
    ${SRC_DIR}/main.cpp
    ${SRC_DIR}/menu.cpp
    ${SRC_DIR}/radar.cpp
    ${DX11_DIR}/moverlay.cpp

    # Dear ImGui 核心
    ${IMGUI_DIR}/imgui.cpp
    ${IMGUI_DIR}/imgui_demo.cpp
    ${IMGUI_DIR}/imgui_draw.cpp
    ${IMGUI_DIR}/imgui_tables.cpp
    ${IMGUI_DIR}/imgui_widgets.cpp

    # Dear ImGui Win32/DX11 后端
    ${IMGUI_DIR}/imgui_impl_dx11.cpp
    ${IMGUI_DIR}/imgui_impl_win32.cpp
)

add_executable(protocol ${SOURCES})

# ── 头文件路径 ────────────────────────────────
target_include_directories(protocol PRIVATE
    ${SRC_DIR}
    ${IMGUI_DIR}
    ${DX11_DIR}
    ${SRC_DIR}/nlohmann
)

# ── 链接库（Windows）──────────────────────────
if (WIN32)
    target_link_libraries(protocol PRIVATE
        d3d11
        dxgi
        d3dcompiler           # VS2022 自带的 d3dcompiler_47.dll
        Shlwapi               # ImGui_ImplWin32 里少量 API 用到
    )
    target_compile_definitions(protocol PRIVATE _CONSOLE)
endif()

# 可选安装规则
# install(TARGETS protocol RUNTIME DESTINATION bin)